ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_CXX_LANGUAGE_STANDARD = c++20
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal" "${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/DoubleConversion" "${PODS_ROOT}/Headers/Public/FBLazyVector" "${PODS_ROOT}/Headers/Public/RCT-Folly" "${PODS_ROOT}/Headers/Public/RCTDeprecation" "${PODS_ROOT}/Headers/Public/RCTRequired" "${PODS_ROOT}/Headers/Public/RCTTypeSafety" "${PODS_ROOT}/Headers/Public/RNCAsyncStorage" "${PODS_ROOT}/Headers/Public/RNScreens" "${PODS_ROOT}/Headers/Public/RNVectorIcons" "${PODS_ROOT}/Headers/Public/React-Core" "${PODS_ROOT}/Headers/Public/React-Fabric" "${PODS_ROOT}/Headers/Public/React-FabricComponents" "${PODS_ROOT}/Headers/Public/React-FabricImage" "${PODS_ROOT}/Headers/Public/React-ImageManager" "${PODS_ROOT}/Headers/Public/React-Mapbuffer" "${PODS_ROOT}/Headers/Public/React-NativeModulesApple" "${PODS_ROOT}/Headers/Public/React-RCTAnimation" "${PODS_ROOT}/Headers/Public/React-RCTAppDelegate" "${PODS_ROOT}/Headers/Public/React-RCTBlob" "${PODS_ROOT}/Headers/Public/React-RCTFBReactNativeSpec" "${PODS_ROOT}/Headers/Public/React-RCTFabric" "${PODS_ROOT}/Headers/Public/React-RCTRuntime" "${PODS_ROOT}/Headers/Public/React-RCTText" "${PODS_ROOT}/Headers/Public/React-RuntimeApple" "${PODS_ROOT}/Headers/Public/React-RuntimeCore" "${PODS_ROOT}/Headers/Public/React-RuntimeHermes" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-cxxreact" "${PODS_ROOT}/Headers/Public/React-debug" "${PODS_ROOT}/Headers/Public/React-defaultsnativemodule" "${PODS_ROOT}/Headers/Public/React-domnativemodule" "${PODS_ROOT}/Headers/Public/React-featureflags" "${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule" "${PODS_ROOT}/Headers/Public/React-graphics" "${PODS_ROOT}/Headers/Public/React-hermes" "${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule" "${PODS_ROOT}/Headers/Public/React-jserrorhandler" "${PODS_ROOT}/Headers/Public/React-jsi" "${PODS_ROOT}/Headers/Public/React-jsiexecutor" "${PODS_ROOT}/Headers/Public/React-jsinspector" "${PODS_ROOT}/Headers/Public/React-jsinspectorcdp" "${PODS_ROOT}/Headers/Public/React-jsinspectornetwork" "${PODS_ROOT}/Headers/Public/React-jsinspectortracing" "${PODS_ROOT}/Headers/Public/React-jsitooling" "${PODS_ROOT}/Headers/Public/React-logger" "${PODS_ROOT}/Headers/Public/React-microtasksnativemodule" "${PODS_ROOT}/Headers/Public/React-oscompat" "${PODS_ROOT}/Headers/Public/React-perflogger" "${PODS_ROOT}/Headers/Public/React-performancetimeline" "${PODS_ROOT}/Headers/Public/React-rendererconsistency" "${PODS_ROOT}/Headers/Public/React-renderercss" "${PODS_ROOT}/Headers/Public/React-rendererdebug" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-runtimescheduler" "${PODS_ROOT}/Headers/Public/React-timing" "${PODS_ROOT}/Headers/Public/React-utils" "${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider" "${PODS_ROOT}/Headers/Public/ReactCodegen" "${PODS_ROOT}/Headers/Public/ReactCommon" "${PODS_ROOT}/Headers/Public/SocketRocket" "${PODS_ROOT}/Headers/Public/VisionCamera" "${PODS_ROOT}/Headers/Public/Yoga" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fast_float" "${PODS_ROOT}/Headers/Public/fmt" "${PODS_ROOT}/Headers/Public/glog" "${PODS_ROOT}/Headers/Public/hermes-engine" "${PODS_ROOT}/Headers/Public/react-native-image-picker" "${PODS_ROOT}/Headers/Public/react-native-safe-area-context" "$(PODS_ROOT)/DoubleConversion" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/Yoga"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "${PODS_CONFIGURATION_BUILD_DIR}/RNVectorIcons" "${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "${PODS_CONFIGURATION_BUILD_DIR}/React-hermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectorcdp" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectornetwork" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling" "${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-oscompat" "${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "${PODS_CONFIGURATION_BUILD_DIR}/VisionCamera" "${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/fmt" "${PODS_CONFIGURATION_BUILD_DIR}/glog" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/VisionCamera/VisionCamera.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTRuntime/React-RCTRuntime.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider/ReactAppDependencyProvider.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/SocketRocket/SocketRocket.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/fmt/fmt.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_cdp/React-jsinspectorcdp.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_network/React-jsinspectornetwork.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_tracing/React-jsinspectortracing.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_css/React-renderercss.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-jsitooling.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap"
OTHER_CPLUSPLUSFLAGS = $(inherited) -DRCT_NEW_ARCH_ENABLED=1
OTHER_LDFLAGS = $(inherited) -ObjC -l"DoubleConversion" -l"RCT-Folly" -l"RCTDeprecation" -l"RCTTypeSafety" -l"RNCAsyncStorage" -l"RNScreens" -l"RNVectorIcons" -l"React-Core" -l"React-CoreModules" -l"React-Fabric" -l"React-FabricComponents" -l"React-FabricImage" -l"React-ImageManager" -l"React-Mapbuffer" -l"React-NativeModulesApple" -l"React-RCTAnimation" -l"React-RCTAppDelegate" -l"React-RCTBlob" -l"React-RCTFBReactNativeSpec" -l"React-RCTFabric" -l"React-RCTImage" -l"React-RCTLinking" -l"React-RCTNetwork" -l"React-RCTRuntime" -l"React-RCTSettings" -l"React-RCTText" -l"React-RCTVibration" -l"React-RuntimeApple" -l"React-RuntimeCore" -l"React-RuntimeHermes" -l"React-cxxreact" -l"React-debug" -l"React-defaultsnativemodule" -l"React-domnativemodule" -l"React-featureflags" -l"React-featureflagsnativemodule" -l"React-graphics" -l"React-hermes" -l"React-idlecallbacksnativemodule" -l"React-jserrorhandler" -l"React-jsi" -l"React-jsiexecutor" -l"React-jsinspector" -l"React-jsinspectorcdp" -l"React-jsinspectornetwork" -l"React-jsinspectortracing" -l"React-jsitooling" -l"React-logger" -l"React-microtasksnativemodule" -l"React-oscompat" -l"React-perflogger" -l"React-performancetimeline" -l"React-rendererconsistency" -l"React-renderercss" -l"React-rendererdebug" -l"React-runtimescheduler" -l"React-utils" -l"ReactAppDependencyProvider" -l"ReactCodegen" -l"ReactCommon" -l"SocketRocket" -l"VisionCamera" -l"Yoga" -l"c++" -l"c++abi" -l"fmt" -l"glog" -l"icucore" -l"react-native-image-picker" -l"react-native-safe-area-context" -framework "AVFoundation" -framework "Accelerate" -framework "AudioToolbox" -framework "CFNetwork" -framework "CoreGraphics" -framework "CoreMedia" -framework "ImageIO" -framework "MobileCoreServices" -framework "Photos" -framework "PhotosUI" -framework "QuartzCore" -framework "Security" -framework "UIKit" -framework "hermes" -weak_framework "JavaScriptCore"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/VisionCamera/VisionCamera.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTRuntime/React-RCTRuntime.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider/ReactAppDependencyProvider.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/SocketRocket/SocketRocket.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/fmt/fmt.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_cdp/React-jsinspectorcdp.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_network/React-jsinspectornetwork.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_tracing/React-jsinspectortracing.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_css/React-renderercss.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-jsitooling.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
SWIFT_INCLUDE_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/VisionCamera"
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
