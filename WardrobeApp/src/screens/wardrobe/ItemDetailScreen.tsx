import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Image, Dimensions, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import {
  Text,
  Card,
  Chip,
  IconButton,
  Menu,
  Divider,
} from 'react-native-paper';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootStackParamList, ClothingItem } from '../../types';
import { ClothingStorage } from '../../utils/storage';
import { formatRelativeTime } from '../../utils/helpers';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';
import FashionButton from '../../components/common/FashionButton';

const { width } = Dimensions.get('window');

type ItemDetailScreenRouteProp = RouteProp<RootStackParamList, 'ItemDetail'>;
type ItemDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ItemDetail'>;

const ItemDetailScreen: React.FC = () => {
  const navigation = useNavigation<ItemDetailScreenNavigationProp>();
  const route = useRoute<ItemDetailScreenRouteProp>();
  const { itemId } = route.params;

  const [item, setItem] = useState<ClothingItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    loadItem();
  }, [itemId]);

  const loadItem = async () => {
    try {
      const items = await ClothingStorage.getClothingItems();
      const foundItem = items.find(item => item.id === itemId);
      setItem(foundItem || null);
    } catch (error) {
      console.error('Failed to load item:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async () => {
    if (!item) return;

    try {
      const updatedItem = { ...item, isFavorite: !item.isFavorite };
      await ClothingStorage.updateClothingItem(item.id, { isFavorite: !item.isFavorite });
      setItem(updatedItem);
    } catch (error) {
      Alert.alert('操作失败', '请稍后重试');
    }
  };

  const handleEdit = () => {
    setMenuVisible(false);
    navigation.navigate('EditItem' as never, { itemId });
  };

  const handleDelete = () => {
    setMenuVisible(false);
    Alert.alert(
      '确认删除',
      '确定要删除这件衣物吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await ClothingStorage.removeClothingItem(itemId);
              navigation.goBack();
            } catch (error) {
              Alert.alert('删除失败', '请稍后重试');
            }
          },
        },
      ]
    );
  };

  const handleAIRecommendation = () => {
    // TODO: 导航到AI推荐页面，传入当前衣物作为核心单品
    Alert.alert('提示', 'AI搭配功能开发中');
  };

  const renderImageGallery = () => {
    if (!item || item.images.length === 0) {
      return (
        <View style={styles.noImageContainer}>
          <Icon name="image" size={64} color="#ccc" />
          <Text style={styles.noImageText}>暂无图片</Text>
        </View>
      );
    }

    return (
      <View style={styles.imageGallery}>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setCurrentImageIndex(index);
          }}
        >
          {item.images.map((uri, index) => (
            <Image
              key={index}
              source={{ uri }}
              style={styles.image}
              resizeMode="cover"
            />
          ))}
        </ScrollView>
        
        {item.images.length > 1 && (
          <View style={styles.imageIndicator}>
            {item.images.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  index === currentImageIndex && styles.activeIndicator
                ]}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Appbar.Header>
          <Appbar.BackAction onPress={() => navigation.goBack()} />
          <Appbar.Content title="加载中..." />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <Text>加载中...</Text>
        </View>
      </View>
    );
  }

  if (!item) {
    return (
      <View style={styles.container}>
        <Appbar.Header>
          <Appbar.BackAction onPress={() => navigation.goBack()} />
          <Appbar.Content title="衣物详情" />
        </Appbar.Header>
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color="#ccc" />
          <Text style={styles.errorText}>衣物不存在</Text>
          <Button mode="contained" onPress={() => navigation.goBack()}>
            返回
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={item.name || '衣物详情'} />
        <IconButton
          icon={item.isFavorite ? 'favorite' : 'favorite-border'}
          iconColor={item.isFavorite ? '#e91e63' : '#666'}
          onPress={toggleFavorite}
        />
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <Appbar.Action
              icon="more-vert"
              onPress={() => setMenuVisible(true)}
            />
          }
        >
          <Menu.Item onPress={handleEdit} title="编辑" leadingIcon="edit" />
          <Menu.Item onPress={handleDelete} title="删除" leadingIcon="delete" />
        </Menu>
      </Appbar.Header>

      <ScrollView style={styles.content}>
        {/* 图片展示 */}
        {renderImageGallery()}

        {/* 基本信息 */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.titleRow}>
              <Text style={styles.itemName}>{item.name || '未命名衣物'}</Text>
              <Chip style={styles.categoryChip}>
                {/* TODO: 从分类ID获取分类名称 */}
                分类
              </Chip>
            </View>
            
            {item.description && (
              <Text style={styles.description}>{item.description}</Text>
            )}

            <View style={styles.metaInfo}>
              <Text style={styles.metaText}>
                添加时间：{formatRelativeTime(item.createdAt)}
              </Text>
              {item.updatedAt !== item.createdAt && (
                <Text style={styles.metaText}>
                  更新时间：{formatRelativeTime(item.updatedAt)}
                </Text>
              )}
            </View>
          </Card.Content>
        </Card>

        {/* 标签 */}
        {item.tags.length > 0 && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.sectionTitle}>标签</Text>
              <View style={styles.tagsContainer}>
                {item.tags.map((tag) => (
                  <Chip
                    key={tag.id}
                    style={[styles.tag, { backgroundColor: tag.color }]}
                    textStyle={{ color: 'white' }}
                  >
                    {tag.name}
                  </Chip>
                ))}
              </View>
            </Card.Content>
          </Card>
        )}

        {/* 操作按钮 */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>快速操作</Text>
            
            <TouchableOpacity
              style={styles.actionItem}
              onPress={handleAIRecommendation}
            >
              <View style={styles.actionContent}>
                <Icon name="auto-awesome" size={24} color="#6200EE" />
                <View style={styles.actionTextContainer}>
                  <Text style={styles.actionTitle}>AI智能搭配</Text>
                  <Text style={styles.actionSubtitle}>
                    以这件衣物为核心，生成搭配推荐
                  </Text>
                </View>
                <Icon name="chevron-right" size={24} color="#666" />
              </View>
            </TouchableOpacity>

            <Divider style={styles.divider} />

            <TouchableOpacity
              style={styles.actionItem}
              onPress={() => Alert.alert('提示', '搭配历史功能开发中')}
            >
              <View style={styles.actionContent}>
                <Icon name="history" size={24} color="#6200EE" />
                <View style={styles.actionTextContainer}>
                  <Text style={styles.actionTitle}>搭配历史</Text>
                  <Text style={styles.actionSubtitle}>
                    查看包含这件衣物的搭配记录
                  </Text>
                </View>
                <Icon name="chevron-right" size={24} color="#666" />
              </View>
            </TouchableOpacity>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    marginVertical: 16,
  },
  imageGallery: {
    height: width,
    backgroundColor: '#000',
  },
  image: {
    width: width,
    height: width,
  },
  imageIndicator: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  activeIndicator: {
    backgroundColor: 'white',
  },
  noImageContainer: {
    height: width,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  noImageText: {
    marginTop: 8,
    fontSize: 16,
    color: '#666',
  },
  card: {
    margin: 16,
    marginBottom: 0,
    elevation: 2,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  itemName: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 12,
  },
  categoryChip: {
    backgroundColor: '#e3f2fd',
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 16,
  },
  metaInfo: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 12,
  },
  metaText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    marginBottom: 4,
  },
  actionItem: {
    paddingVertical: 12,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionTextContainer: {
    flex: 1,
    marginLeft: 16,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  divider: {
    marginVertical: 8,
  },
});

export default ItemDetailScreen;
