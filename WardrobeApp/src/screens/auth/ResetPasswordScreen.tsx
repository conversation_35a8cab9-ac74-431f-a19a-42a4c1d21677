import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, TouchableOpacity } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Appbar,
  HelperText
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types';
import { validateEmail, validatePhone, validatePassword } from '../../utils/helpers';

type ResetPasswordScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ResetPassword'>;

type Step = 'verify' | 'reset';

interface FormData {
  account: string;
  verificationCode: string;
  newPassword: string;
  confirmPassword: string;
}

interface FormErrors {
  account?: string;
  verificationCode?: string;
  newPassword?: string;
  confirmPassword?: string;
}

const ResetPasswordScreen: React.FC = () => {
  const navigation = useNavigation<ResetPasswordScreenNavigationProp>();
  const [accountType, setAccountType] = useState<'phone' | 'email'>('phone');
  const [currentStep, setCurrentStep] = useState<Step>('verify');
  const [formData, setFormData] = useState<FormData>({
    account: '',
    verificationCode: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const validateStep1 = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.account.trim()) {
      newErrors.account = accountType === 'phone' ? '请输入手机号' : '请输入邮箱';
    } else if (accountType === 'phone' && !validatePhone(formData.account)) {
      newErrors.account = '请输入正确的手机号';
    } else if (accountType === 'email' && !validateEmail(formData.account)) {
      newErrors.account = '请输入正确的邮箱地址';
    }

    if (!formData.verificationCode.trim()) {
      newErrors.verificationCode = '请输入验证码';
    } else if (formData.verificationCode.length !== 6) {
      newErrors.verificationCode = '验证码为6位数字';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = (): boolean => {
    const newErrors: FormErrors = {};

    const passwordValidation = validatePassword(formData.newPassword);
    if (!passwordValidation.isValid) {
      newErrors.newPassword = passwordValidation.errors[0];
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const sendVerificationCode = async () => {
    if (!formData.account.trim()) {
      setErrors({ account: accountType === 'phone' ? '请输入手机号' : '请输入邮箱' });
      return;
    }

    if (accountType === 'phone' && !validatePhone(formData.account)) {
      setErrors({ account: '请输入正确的手机号' });
      return;
    }

    if (accountType === 'email' && !validateEmail(formData.account)) {
      setErrors({ account: '请输入正确的邮箱地址' });
      return;
    }

    setLoading(true);

    try {
      // 模拟发送验证码
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert(
        '验证码已发送',
        `验证码已发送至您的${accountType === 'phone' ? '手机' : '邮箱'}，请注意查收`
      );

      setCountdown(60);
      setErrors({});
    } catch (error) {
      Alert.alert('发送失败', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const verifyCode = async () => {
    if (!validateStep1()) {
      return;
    }

    setLoading(true);

    try {
      // 模拟验证码验证
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟验证成功
      if (formData.verificationCode === '123456') {
        setCurrentStep('reset');
        setErrors({});
      } else {
        setErrors({ verificationCode: '验证码错误，请重新输入' });
      }
    } catch (error) {
      Alert.alert('验证失败', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async () => {
    if (!validateStep2()) {
      return;
    }

    setLoading(true);

    try {
      // 模拟密码重置
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert(
        '密码重置成功',
        '您的密码已成功重置，请使用新密码登录',
        [
          {
            text: '去登录',
            onPress: () => navigation.navigate('Login'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('重置失败', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const renderVerifyStep = () => (
    <>
      <View style={styles.header}>
        <Text style={styles.title}>找回密码</Text>
        <Text style={styles.subtitle}>
          {accountType === 'phone' ? '手机号验证' : '邮箱验证'}
        </Text>
      </View>

      <Card style={styles.card}>
        <Card.Content>
          {/* 账号类型切换 */}
          <View style={styles.accountTypeContainer}>
            <TouchableOpacity
              style={[
                styles.accountTypeButton,
                accountType === 'phone' && styles.accountTypeButtonActive
              ]}
              onPress={() => {
                setAccountType('phone');
                setFormData(prev => ({ ...prev, account: '' }));
                setErrors(prev => ({ ...prev, account: undefined }));
              }}
            >
              <Text style={[
                styles.accountTypeText,
                accountType === 'phone' && styles.accountTypeTextActive
              ]}>
                手机号
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.accountTypeButton,
                accountType === 'email' && styles.accountTypeButtonActive
              ]}
              onPress={() => {
                setAccountType('email');
                setFormData(prev => ({ ...prev, account: '' }));
                setErrors(prev => ({ ...prev, account: undefined }));
              }}
            >
              <Text style={[
                styles.accountTypeText,
                accountType === 'email' && styles.accountTypeTextActive
              ]}>
                邮箱
              </Text>
            </TouchableOpacity>
          </View>

          {/* 账号输入 */}
          <TextInput
            label={accountType === 'phone' ? '手机号' : '邮箱'}
            value={formData.account}
            onChangeText={(text) => updateFormData('account', text)}
            mode="outlined"
            style={styles.input}
            keyboardType={accountType === 'phone' ? 'phone-pad' : 'email-address'}
            autoCapitalize="none"
            error={!!errors.account}
          />
          <HelperText type="error" visible={!!errors.account}>
            {errors.account}
          </HelperText>

          {/* 验证码输入 */}
          <View style={styles.verificationContainer}>
            <TextInput
              label="验证码"
              value={formData.verificationCode}
              onChangeText={(text) => updateFormData('verificationCode', text)}
              mode="outlined"
              style={styles.verificationInput}
              keyboardType="number-pad"
              maxLength={6}
              error={!!errors.verificationCode}
            />
            <Button
              mode="outlined"
              onPress={sendVerificationCode}
              disabled={countdown > 0 || loading}
              style={styles.sendCodeButton}
              compact
            >
              {countdown > 0 ? `${countdown}s` : '发送验证码'}
            </Button>
          </View>
          <HelperText type="error" visible={!!errors.verificationCode}>
            {errors.verificationCode}
          </HelperText>

          {/* 验证按钮 */}
          <Button
            mode="contained"
            onPress={verifyCode}
            loading={loading}
            disabled={loading}
            style={styles.actionButton}
            contentStyle={styles.actionButtonContent}
          >
            验证
          </Button>
        </Card.Content>
      </Card>
    </>
  );

  const renderResetStep = () => (
    <>
      <View style={styles.header}>
        <Text style={styles.title}>重置密码</Text>
        <Text style={styles.subtitle}>请设置新的登录密码</Text>
      </View>

      <Card style={styles.card}>
        <Card.Content>
          {/* 新密码输入 */}
          <TextInput
            label="新密码"
            value={formData.newPassword}
            onChangeText={(text) => updateFormData('newPassword', text)}
            mode="outlined"
            style={styles.input}
            secureTextEntry={!showPassword}
            error={!!errors.newPassword}
            right={
              <TextInput.Icon
                icon={showPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />
          <HelperText type="error" visible={!!errors.newPassword}>
            {errors.newPassword}
          </HelperText>

          {/* 确认密码输入 */}
          <TextInput
            label="确认新密码"
            value={formData.confirmPassword}
            onChangeText={(text) => updateFormData('confirmPassword', text)}
            mode="outlined"
            style={styles.input}
            secureTextEntry={!showConfirmPassword}
            error={!!errors.confirmPassword}
            right={
              <TextInput.Icon
                icon={showConfirmPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              />
            }
          />
          <HelperText type="error" visible={!!errors.confirmPassword}>
            {errors.confirmPassword}
          </HelperText>

          {/* 重置按钮 */}
          <Button
            mode="contained"
            onPress={resetPassword}
            loading={loading}
            disabled={loading}
            style={styles.actionButton}
            contentStyle={styles.actionButtonContent}
          >
            重置密码
          </Button>
        </Card.Content>
      </Card>
    </>
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="找回密码" />
      </Appbar.Header>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {currentStep === 'verify' ? renderVerifyStep() : renderResetStep()}

        {/* 返回登录链接 */}
        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>想起密码了？</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.loginLink}>返回登录</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  card: {
    elevation: 4,
    marginBottom: 30,
  },
  accountTypeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 4,
  },
  accountTypeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  accountTypeButtonActive: {
    backgroundColor: '#6200EE',
  },
  accountTypeText: {
    color: '#666',
    fontWeight: '500',
  },
  accountTypeTextActive: {
    color: 'white',
  },
  input: {
    marginBottom: 8,
  },
  verificationContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  verificationInput: {
    flex: 1,
    marginBottom: 8,
  },
  sendCodeButton: {
    marginTop: 8,
    minWidth: 100,
  },
  actionButton: {
    marginTop: 20,
  },
  actionButtonContent: {
    paddingVertical: 8,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    color: '#666',
    marginRight: 4,
  },
  loginLink: {
    color: '#6200EE',
    fontWeight: '500',
  },
});

export default ResetPasswordScreen;
