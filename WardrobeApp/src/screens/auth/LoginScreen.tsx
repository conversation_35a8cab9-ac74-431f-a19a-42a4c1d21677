import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, SafeAreaView, TouchableOpacity } from 'react-native';
import {
  Text,
  TextInput,
  Card,
  Divider,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootStackParamList } from '../../types';
import { UserStorage } from '../../utils/storage';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';
import FashionButton from '../../components/common/FashionButton';

type LoginScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Login'>;

const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const [loginType, setLoginType] = useState<'phone' | 'email'>('phone');
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    if (!account || !password) {
      Alert.alert('提示', '请输入账号和密码');
      return;
    }

    setLoading(true);
    
    // 模拟登录请求
    setTimeout(() => {
      setLoading(false);
      // 登录成功，跳转到主页面
      navigation.replace('Main');
    }, 1500);
  };

  const handleAppleLogin = () => {
    Alert.alert('提示', 'Apple ID登录功能开发中');
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>欢迎回来</Text>
        <Text style={styles.subtitle}>登录您的衣柜管理账户</Text>
      </View>

      <Card style={styles.card}>
        <Card.Content>
          {/* 登录方式切换 */}
          <View style={styles.loginTypeContainer}>
            <TouchableOpacity
              style={[
                styles.loginTypeButton,
                loginType === 'phone' && styles.loginTypeButtonActive
              ]}
              onPress={() => setLoginType('phone')}
            >
              <Text style={[
                styles.loginTypeText,
                loginType === 'phone' && styles.loginTypeTextActive
              ]}>
                手机号
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.loginTypeButton,
                loginType === 'email' && styles.loginTypeButtonActive
              ]}
              onPress={() => setLoginType('email')}
            >
              <Text style={[
                styles.loginTypeText,
                loginType === 'email' && styles.loginTypeTextActive
              ]}>
                邮箱
              </Text>
            </TouchableOpacity>
          </View>

          {/* 账号输入 */}
          <TextInput
            label={loginType === 'phone' ? '手机号' : '邮箱'}
            value={account}
            onChangeText={setAccount}
            mode="outlined"
            style={styles.input}
            keyboardType={loginType === 'phone' ? 'phone-pad' : 'email-address'}
            autoCapitalize="none"
          />

          {/* 密码输入 */}
          <TextInput
            label="密码"
            value={password}
            onChangeText={setPassword}
            mode="outlined"
            style={styles.input}
            secureTextEntry={!showPassword}
            right={
              <TextInput.Icon
                icon={showPassword ? 'eye-off' : 'eye'}
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />

          {/* 忘记密码 */}
          <TouchableOpacity
            style={styles.forgotPassword}
            onPress={() => navigation.navigate('ResetPassword')}
          >
            <Text style={styles.forgotPasswordText}>忘记密码？</Text>
          </TouchableOpacity>

          {/* 登录按钮 */}
          <Button
            mode="contained"
            onPress={handleLogin}
            loading={loading}
            disabled={loading}
            style={styles.loginButton}
            contentStyle={styles.loginButtonContent}
          >
            登录
          </Button>

          {/* 分割线 */}
          <View style={styles.dividerContainer}>
            <Divider style={styles.divider} />
            <Text style={styles.dividerText}>或</Text>
            <Divider style={styles.divider} />
          </View>

          {/* Apple ID登录 */}
          <Button
            mode="outlined"
            onPress={handleAppleLogin}
            style={styles.appleButton}
            contentStyle={styles.appleButtonContent}
            icon={() => <Icon name="apple" size={20} color="#000" />}
          >
            使用 Apple ID 登录
          </Button>

          {/* 注册链接 */}
          <View style={styles.registerContainer}>
            <Text style={styles.registerText}>还没有账户？</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.registerLink}>立即注册</Text>
            </TouchableOpacity>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  contentContainer: {
    padding: 20,
    paddingTop: 60,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  card: {
    elevation: 4,
  },
  loginTypeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 4,
  },
  loginTypeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  loginTypeButtonActive: {
    backgroundColor: '#6200EE',
  },
  loginTypeText: {
    color: '#666',
    fontWeight: '500',
  },
  loginTypeTextActive: {
    color: 'white',
  },
  input: {
    marginBottom: 16,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 20,
  },
  forgotPasswordText: {
    color: '#6200EE',
    fontSize: 14,
  },
  loginButton: {
    marginBottom: 20,
  },
  loginButtonContent: {
    paddingVertical: 8,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  divider: {
    flex: 1,
  },
  dividerText: {
    marginHorizontal: 16,
    color: '#666',
  },
  appleButton: {
    marginBottom: 20,
    borderColor: '#000',
  },
  appleButtonContent: {
    paddingVertical: 8,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  registerText: {
    color: '#666',
    marginRight: 4,
  },
  registerLink: {
    color: '#6200EE',
    fontWeight: '500',
  },
});

export default LoginScreen;
