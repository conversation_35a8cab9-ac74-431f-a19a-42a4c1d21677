import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, TouchableOpacity } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Appbar,
  Checkbox,
  HelperText
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../types';
import { validateEmail, validatePhone, validatePassword } from '../../utils/helpers';
import { UserStorage } from '../../utils/storage';

type RegisterScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Register'>;

interface FormData {
  nickname: string;
  account: string;
  password: string;
  confirmPassword: string;
  agreeTerms: boolean;
}

interface FormErrors {
  nickname?: string;
  account?: string;
  password?: string;
  confirmPassword?: string;
  agreeTerms?: string;
}

const RegisterScreen: React.FC = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>();
  const [registerType, setRegisterType] = useState<'phone' | 'email'>('phone');
  const [formData, setFormData] = useState<FormData>({
    nickname: '',
    account: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 验证昵称
    if (!formData.nickname.trim()) {
      newErrors.nickname = '请输入昵称';
    } else if (formData.nickname.trim().length < 2) {
      newErrors.nickname = '昵称至少2个字符';
    }

    // 验证账号
    if (!formData.account.trim()) {
      newErrors.account = registerType === 'phone' ? '请输入手机号' : '请输入邮箱';
    } else if (registerType === 'phone' && !validatePhone(formData.account)) {
      newErrors.account = '请输入正确的手机号';
    } else if (registerType === 'email' && !validateEmail(formData.account)) {
      newErrors.account = '请输入正确的邮箱地址';
    }

    // 验证密码
    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      newErrors.password = passwordValidation.errors[0];
    }

    // 验证确认密码
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    // 验证协议
    if (!formData.agreeTerms) {
      newErrors.agreeTerms = '请同意用户协议和隐私政策';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // 模拟注册请求
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 模拟用户数据
      const userData = {
        id: Date.now().toString(),
        nickname: formData.nickname,
        account: formData.account,
        accountType: registerType,
        avatar: '',
        createdAt: new Date().toISOString(),
      };

      // 保存用户信息
      await UserStorage.saveUserInfo(userData);
      await UserStorage.saveUserToken('mock_token_' + userData.id);

      Alert.alert(
        '注册成功',
        '欢迎加入衣柜管理！',
        [
          {
            text: '开始使用',
            onPress: () => navigation.replace('Main'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('注册失败', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="注册账户" />
      </Appbar.Header>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>创建新账户</Text>
          <Text style={styles.subtitle}>填写以下信息完成注册</Text>
        </View>

        <Card style={styles.card}>
          <Card.Content>
            {/* 注册方式切换 */}
            <View style={styles.registerTypeContainer}>
              <TouchableOpacity
                style={[
                  styles.registerTypeButton,
                  registerType === 'phone' && styles.registerTypeButtonActive
                ]}
                onPress={() => {
                  setRegisterType('phone');
                  setFormData(prev => ({ ...prev, account: '' }));
                  setErrors(prev => ({ ...prev, account: undefined }));
                }}
              >
                <Text style={[
                  styles.registerTypeText,
                  registerType === 'phone' && styles.registerTypeTextActive
                ]}>
                  手机号注册
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.registerTypeButton,
                  registerType === 'email' && styles.registerTypeButtonActive
                ]}
                onPress={() => {
                  setRegisterType('email');
                  setFormData(prev => ({ ...prev, account: '' }));
                  setErrors(prev => ({ ...prev, account: undefined }));
                }}
              >
                <Text style={[
                  styles.registerTypeText,
                  registerType === 'email' && styles.registerTypeTextActive
                ]}>
                  邮箱注册
                </Text>
              </TouchableOpacity>
            </View>

            {/* 昵称输入 */}
            <TextInput
              label="昵称"
              value={formData.nickname}
              onChangeText={(text) => updateFormData('nickname', text)}
              mode="outlined"
              style={styles.input}
              error={!!errors.nickname}
              maxLength={20}
            />
            <HelperText type="error" visible={!!errors.nickname}>
              {errors.nickname}
            </HelperText>

            {/* 账号输入 */}
            <TextInput
              label={registerType === 'phone' ? '手机号' : '邮箱'}
              value={formData.account}
              onChangeText={(text) => updateFormData('account', text)}
              mode="outlined"
              style={styles.input}
              keyboardType={registerType === 'phone' ? 'phone-pad' : 'email-address'}
              autoCapitalize="none"
              error={!!errors.account}
            />
            <HelperText type="error" visible={!!errors.account}>
              {errors.account}
            </HelperText>

            {/* 密码输入 */}
            <TextInput
              label="密码"
              value={formData.password}
              onChangeText={(text) => updateFormData('password', text)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              error={!!errors.password}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
            />
            <HelperText type="error" visible={!!errors.password}>
              {errors.password}
            </HelperText>

            {/* 确认密码输入 */}
            <TextInput
              label="确认密码"
              value={formData.confirmPassword}
              onChangeText={(text) => updateFormData('confirmPassword', text)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showConfirmPassword}
              error={!!errors.confirmPassword}
              right={
                <TextInput.Icon
                  icon={showConfirmPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                />
              }
            />
            <HelperText type="error" visible={!!errors.confirmPassword}>
              {errors.confirmPassword}
            </HelperText>

            {/* 用户协议 */}
            <View style={styles.agreementContainer}>
              <Checkbox
                status={formData.agreeTerms ? 'checked' : 'unchecked'}
                onPress={() => updateFormData('agreeTerms', !formData.agreeTerms)}
              />
              <View style={styles.agreementTextContainer}>
                <Text style={styles.agreementText}>
                  我已阅读并同意
                  <Text style={styles.linkText}> 用户协议 </Text>
                  和
                  <Text style={styles.linkText}> 隐私政策</Text>
                </Text>
              </View>
            </View>
            <HelperText type="error" visible={!!errors.agreeTerms}>
              {errors.agreeTerms}
            </HelperText>

            {/* 注册按钮 */}
            <Button
              mode="contained"
              onPress={handleRegister}
              loading={loading}
              disabled={loading}
              style={styles.registerButton}
              contentStyle={styles.registerButtonContent}
            >
              注册
            </Button>

            {/* 登录链接 */}
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>已有账户？</Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('Login')}
              >
                <Text style={styles.loginLink}>立即登录</Text>
              </TouchableOpacity>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  card: {
    elevation: 4,
  },
  registerTypeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 4,
  },
  registerTypeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  registerTypeButtonActive: {
    backgroundColor: '#6200EE',
  },
  registerTypeText: {
    color: '#666',
    fontWeight: '500',
  },
  registerTypeTextActive: {
    color: 'white',
  },
  input: {
    marginBottom: 8,
  },
  agreementContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginVertical: 16,
  },
  agreementTextContainer: {
    flex: 1,
    marginLeft: 8,
  },
  agreementText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  linkText: {
    color: '#6200EE',
    fontWeight: '500',
  },
  registerButton: {
    marginTop: 20,
    marginBottom: 20,
  },
  registerButtonContent: {
    paddingVertical: 8,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    color: '#666',
    marginRight: 4,
  },
  loginLink: {
    color: '#6200EE',
    fontWeight: '500',
  },
});

export default RegisterScreen;
