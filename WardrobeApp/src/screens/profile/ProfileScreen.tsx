import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import {
  Text,
  Card,
  Avatar,
  List,
  Divider,
  Button,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { User } from '../../types';
import { DataService } from '../../services/DataService';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';
import FashionButton from '../../components/common/FashionButton';
import { UserStorage } from '../../utils/storage';

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const [user, setUser] = useState<User | null>(null);
  const [statistics, setStatistics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      const [userData, stats] = await Promise.all([
        DataService.getCurrentUser(),
        DataService.getStatistics(),
      ]);
      setUser(userData);
      setStatistics(stats);
    } catch (error) {
      console.error('Failed to load user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      '确认退出',
      '确定要退出登录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '退出',
          style: 'destructive',
          onPress: async () => {
            try {
              await UserStorage.logout();
              navigation.navigate('Login' as never);
            } catch (error) {
              Alert.alert('退出失败', '请稍后重试');
            }
          },
        },
      ]
    );
  };

  const menuItems = [
    {
      title: '收藏夹',
      description: `${statistics?.favoriteItems || 0} 件单品，${statistics?.favoriteRecommendations || 0} 套搭配`,
      icon: 'favorite',
      onPress: () => navigation.navigate('Favorites' as never),
    },
    {
      title: '搭配历史',
      description: '查看历史搭配记录',
      icon: 'history',
      onPress: () => Alert.alert('提示', '搭配历史功能开发中'),
    },
    {
      title: 'AI补全衣橱',
      description: '分析衣橱并推荐缺失单品',
      icon: 'auto-awesome',
      onPress: () => Alert.alert('提示', 'AI补全功能开发中'),
    },
    {
      title: '设置',
      description: '个人设置和偏好',
      icon: 'settings',
      onPress: () => navigation.navigate('Settings' as never),
    },
    {
      title: '账号绑定',
      description: '绑定第三方账号',
      icon: 'link',
      onPress: () => Alert.alert('提示', '账号绑定功能开发中'),
    },
    {
      title: '通知中心',
      description: '查看系统通知',
      icon: 'notifications',
      onPress: () => Alert.alert('提示', '通知中心功能开发中'),
    },
    {
      title: '等级积分',
      description: '查看等级和积分',
      icon: 'stars',
      onPress: () => Alert.alert('提示', '等级积分功能开发中'),
    },
  ];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>PROFILE</Text>
          <TouchableOpacity style={styles.settingsButton}>
            <Icon name="settings" size={24} color={FashionDesignSystem.colors.primaryText} />
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with title and settings - like wardrobe page */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>PROFILE</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Icon name="settings" size={24} color={FashionDesignSystem.colors.primaryText} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 用户信息卡片 */}
        <Card style={styles.userCard}>
          <Card.Content>
            <View style={styles.userInfo}>
              <Avatar.Text
                size={80}
                label={user?.nickname?.charAt(0) || 'U'}
                style={styles.avatar}
              />
              <View style={styles.userDetails}>
                <Text style={styles.nickname}>{user?.nickname || '未设置昵称'}</Text>
                <Text style={styles.account}>
                  {user?.email || user?.phone || '未绑定账号'}
                </Text>
                <Text style={styles.joinDate}>
                  加入时间：{user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : '未知'}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* 统计信息 */}
        {statistics && (
          <Card style={styles.statsCard}>
            <Card.Content>
              <Text style={styles.statsTitle}>我的衣橱</Text>
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{statistics.totalItems}</Text>
                  <Text style={styles.statLabel}>总衣物</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{statistics.favoriteItems}</Text>
                  <Text style={styles.statLabel}>收藏单品</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{statistics.totalRecommendations}</Text>
                  <Text style={styles.statLabel}>推荐记录</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{statistics.totalCategories}</Text>
                  <Text style={styles.statLabel}>分类数量</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* 功能菜单 */}
        <Card style={styles.menuCard}>
          <Card.Content style={styles.menuContent}>
            {menuItems.map((item, index) => (
              <React.Fragment key={item.title}>
                <TouchableOpacity onPress={item.onPress}>
                  <List.Item
                    title={item.title}
                    description={item.description}
                    left={(props) => <List.Icon {...props} icon={item.icon} />}
                    right={(props) => <List.Icon {...props} icon="chevron-right" />}
                  />
                </TouchableOpacity>
                {index < menuItems.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </Card.Content>
        </Card>

        {/* 退出登录 */}
        <Button
          mode="outlined"
          onPress={handleLogout}
          style={styles.logoutButton}
          textColor="#d32f2f"
          icon="logout"
        >
          退出登录
        </Button>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FashionDesignSystem.colors.background,
  },

  // Header styles - like wardrobe page
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    backgroundColor: FashionDesignSystem.colors.background,
  },

  headerTitle: {
    ...FashionDesignSystem.typography.title,
    fontWeight: '700',
    letterSpacing: 1,
  },

  settingsButton: {
    padding: FashionDesignSystem.spacing.sm,
  },

  content: {
    flex: 1,
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  loadingText: {
    ...FashionDesignSystem.typography.body,
    color: FashionDesignSystem.colors.secondaryText,
  },
  userCard: {
    backgroundColor: FashionDesignSystem.colors.surface,
    borderRadius: FashionDesignSystem.borderRadius.card,
    padding: FashionDesignSystem.spacing.xl,
    marginBottom: FashionDesignSystem.spacing.xl,
    ...FashionDesignSystem.shadows.soft,
  },

  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  avatar: {
    backgroundColor: FashionDesignSystem.colors.accent,
    marginRight: FashionDesignSystem.spacing.lg,
  },

  userDetails: {
    flex: 1,
  },

  nickname: {
    ...FashionDesignSystem.typography.subtitle,
    fontWeight: '600',
    marginBottom: FashionDesignSystem.spacing.xs,
  },

  account: {
    ...FashionDesignSystem.typography.body,
    color: FashionDesignSystem.colors.secondaryText,
    marginBottom: FashionDesignSystem.spacing.xs,
  },

  joinDate: {
    ...FashionDesignSystem.typography.caption,
    color: FashionDesignSystem.colors.lightText,
  },
  statsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6200EE',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  menuCard: {
    marginBottom: 16,
    elevation: 2,
  },
  menuContent: {
    paddingVertical: 0,
  },
  logoutButton: {
    marginTop: 16,
    marginBottom: 32,
    borderColor: '#d32f2f',
  },
});

export default ProfileScreen;
