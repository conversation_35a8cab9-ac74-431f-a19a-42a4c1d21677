import*as e from"../intl-messageformat/intl-messageformat.js";const t={};class r{filename;stringStructure;localizedMessages;localizedStringSet;constructor(e,t,r){this.filename=e,this.stringStructure=t,this.localizedMessages=r}getLocalizedStringSetFor(e){if(this.localizedStringSet)return this.localizedStringSet;const t=this.localizedMessages.get(e);if(!t)throw new Error(`No locale data registered for '${e}'`);return this.localizedStringSet=new s(this.filename,this.stringStructure,e,t),this.localizedStringSet}}class s{filename;stringStructure;localizedMessages;cachedSimpleStrings=new Map;cachedMessageFormatters=new Map;localeForFormatter;constructor(e,t,r,s){this.filename=e,this.stringStructure=t,this.localizedMessages=s,this.localeForFormatter="en-XA"===r||"en-XL"===r?"de-DE":r}getLocalizedString(e,r=t){return r===t||0===Object.keys(r).length?this.getSimpleLocalizedString(e):this.getFormattedLocalizedString(e,r)}getMessageFormatterFor(t){const r=Object.keys(this.stringStructure).find((e=>this.stringStructure[e]===t));if(!r)throw new Error(`Unable to locate '${t}' in UIStrings object`);const s=`${this.filename} | ${r}`,a=this.localizedMessages[s],i=a?a.message:t;return new e.IntlMessageFormat(i,this.localeForFormatter,void 0,{ignoreTag:!0})}getSimpleLocalizedString(e){const t=this.cachedSimpleStrings.get(e);if(t)return t;const r=this.getMessageFormatterFor(e).format();return this.cachedSimpleStrings.set(e,r),r}getFormattedLocalizedString(t,r){let s=this.cachedMessageFormatters.get(t);s||(s=this.getMessageFormatterFor(t),this.cachedMessageFormatters.set(t,s));try{return s.format(r)}catch(s){return new e.IntlMessageFormat(t,this.localeForFormatter,void 0,{ignoreTag:!0}).format(r)}}}var a=Object.freeze({__proto__:null,RegisteredFileStrings:r,LocalizedStringSet:s});var i=Object.freeze({__proto__:null,I18n:class{supportedLocales;localeData=new Map;defaultLocale;constructor(e,t){this.defaultLocale=t,this.supportedLocales=new Set(e)}registerLocaleData(e,t){this.localeData.set(e,t)}hasLocaleDataForTest(e){return this.localeData.has(e)}resetLocaleDataForTest(){this.localeData.clear()}registerFileStrings(e,t){return new r(e,t,this.localeData)}lookupClosestSupportedLocale(e){const t=Intl.getCanonicalLocales(e)[0].split("-");for(;t.length;){const e=t.join("-");if(this.supportedLocales.has(e))return e;t.pop()}return this.defaultLocale}}});export{i as I18n,a as LocalizedStringSet};
