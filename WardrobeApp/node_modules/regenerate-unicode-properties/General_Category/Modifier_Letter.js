const set = require('regenerate')(0x2EC, 0x2EE, 0x374, 0x37A, 0x559, 0x640, 0x7FA, 0x81A, 0x824, 0x828, 0x8C9, 0x971, 0xE46, 0xEC6, 0x10FC, 0x17D7, 0x1843, 0x1AA7, 0x1D78, 0x2071, 0x207F, 0x2D6F, 0x2E2F, 0x3005, 0x303B, 0xA015, 0xA60C, 0xA67F, 0xA770, 0xA788, 0xA9CF, 0xA9E6, 0xAA70, 0xAADD, 0xAB69, 0xFF70, 0x10D4E, 0x10D6F, 0x16FE3, 0x1E4EB, 0x1E94B);
set.addRange(0x2B0, 0x2C1).addRange(0x2C6, 0x2D1).addRange(0x2E0, 0x2E4).addRange(0x6E5, 0x6E6).addRange(0x7F4, 0x7F5).addRange(0x1C78, 0x1C7D).addRange(0x1D2C, 0x1D6A).addRange(0x1D9B, 0x1DBF).addRange(0x2090, 0x209C).addRange(0x2C7C, 0x2C7D).addRange(0x3031, 0x3035).addRange(0x309D, 0x309E).addRange(0x30FC, 0x30FE).addRange(0xA4F8, 0xA4FD).addRange(0xA69C, 0xA69D).addRange(0xA717, 0xA71F).addRange(0xA7F2, 0xA7F4).addRange(0xA7F8, 0xA7F9).addRange(0xAAF3, 0xAAF4).addRange(0xAB5C, 0xAB5F).addRange(0xFF9E, 0xFF9F).addRange(0x10780, 0x10785).addRange(0x10787, 0x107B0).addRange(0x107B2, 0x107BA).addRange(0x16B40, 0x16B43).addRange(0x16D40, 0x16D42).addRange(0x16D6B, 0x16D6C).addRange(0x16F93, 0x16F9F).addRange(0x16FE0, 0x16FE1).addRange(0x1AFF0, 0x1AFF3).addRange(0x1AFF5, 0x1AFFB).addRange(0x1AFFD, 0x1AFFE).addRange(0x1E030, 0x1E06D).addRange(0x1E137, 0x1E13D);
exports.characters = set;
