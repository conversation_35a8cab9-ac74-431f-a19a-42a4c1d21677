---
type: "always_apply"
---

#开发工具规范
- Create a Next.js middleware that checks for a valid JWT in cookies and redirects unauthenticated users to `/login`. use context7
- Configure a Cloudflare Worker script to cache JSON API responses for five minutes. use context7
- 使用PowerShell的Get-ChildItem命令读取项目目录结构和文件列表
- 使用PowerShell的Get-Content命令读取文件内容，指定适当的编码参数(-Encoding)
- 使用PowerShell的Set-Content命令写入文件内容，确保指定正确的编码参数
- 使用PowerShell的Select-String命令进行文件内容搜索，支持正则表达式
- 使用PowerShell的Replace方法替换文件内容，注意正确处理特殊字符
- 使用PowerShell的Write-Host命令输出信息，适当使用颜色参数增强可读性
- 使用PowerShell的Read-Host命令读取用户输入，提供清晰的提示信息

#代码质量管理
- 每次重大功能开发完成后必须执行全面代码审查，包括：
- 识别并移除所有未使用的代码段和函数
- 重构并消除重复代码，提取共用功能为独立函数或类
- 移除所有调试语句和临时注释
- 删除未被引用的资源文件和依赖项
- 确保代码库保持高效、整洁和可维护状态，代码复杂度控制在合理范围内
- 定期检查项目并删除未使用的代码和文件，保持代码库整洁

#开发工作流程
使用codebase-retrieval工具获取模板文件信息，查询时使用具体明确的关键词
使用str-replace-editor工具进行代码修改和优化，确保修改前后代码的一致性和完整性
在提交文件或解决方案前，必须先进行自检以确保其功能正常，包括语法检查和基本功能测试
处理大型文件时必须采用分步执行策略，将大型修改拆分为多个小步骤
实施适当的错误处理机制，确保操作不会因文件大小或复杂度而中断
对于复杂任务，实施进度跟踪和错误恢复机制，确保任务可以从中断点继续
处理大文件时，将任务分解为多个小步骤，每步处理不超过300行代码，避免工具输入过大导致错误

项目代码清理与优化任务
请对我的项目进行全面的代码审查和清理，以提高代码质量和系统性能。具体执行以下任务：
1. 系统性代码审查
系统地检查项目中的所有Python文件（.py）、HTML模板（.html）、CSS样式表（.css）和JavaScript文件（.js）
使用静态代码分析工具（如PyLint或Flake8）识别潜在问题
检查代码是否符合PEP 8规范和项目的编码风格指南
2. 移除未使用的代码元素
识别并标记所有未被引用或调用的函数、类和变量
特别关注视图函数、模型类和工具函数
检查导入但未使用的模块和库，并移除它们
确保保留那些虽然看似未使用但实际上通过反射或动态加载方式使用的代码
3. 清理冗余代码
移除所有被注释掉的代码块，除非它们包含明确标记为"保留"的重要开发说明
删除调试用的print语句和日志输出
移除重复或功能相似的代码，考虑重构为通用函数
4. 文件系统清理
删除空白文件、重复文件或明确标记为测试/临时的文件
检查并移除未在项目中引用的静态资源（图片、字体、CSS、JS等）
确保所有文件都放置在合适的目录结构中，遵循MVC架构原则
5. 前端资源优化
分析并优化CSS文件，移除未使用的样式规则
检查JavaScript文件，移除未调用的函数和变量
合并可以合并的CSS和JavaScript文件，减少HTTP请求数量
6. 项目结构优化
检查项目目录结构，确保符合Flask应用的最佳实践
将相关功能的文件组织在一起，提高代码可维护性
确保配置文件、静态资源和模板文件位于正确的位置
7. 执行流程
首先提供一份初步分析报告，列出所有可能需要删除或修改的内容
在执行任何实际删除或修改操作前获取我的明确确认
对于每类问题（未使用代码、注释代码、冗余文件等），分别请求确认
特别是对可能影响系统功能的更改，提供详细的影响分析
8. 最终交付
提供一份详细的清理报告，包括：
所有被删除的内容及其位置
每项删除的理由
对项目结构的改进建议
潜在的性能优化点
确保项目在清理后能够正常运行，所有功能保持完整